import { responseModel } from '../../../response/models';
import { logger } from '../../../../global/services';
import { Var } from '../../../../global/var';
import { Op } from 'sequelize';

export interface ResponseFilters {
  timeFilter?: string;
  statusFilter?: string;
  searchQuery?: string;
  page?: number;
  limit?: number;
}

/**
 * Data Access Layer function to retrieve survey responses and analytics data
 *
 * This function fetches responses for a specific survey with filtering and pagination support.
 * Used for displaying response analytics and generating insights from survey data.
 *
 * Security features:
 * - Validates account ownership by including account_id in query
 * - Comprehensive error handling with logging
 *
 * Performance considerations:
 * - Orders by creation date (newest first) for better UX
 * - Supports pagination to handle large datasets
 * - Uses indexed fields (survey_id, account_id) for optimal query performance
 *
 * @param surveyId - The unique UUID of the survey whose responses to retrieve
 * @param accountId - The unique UUID of the account that owns the survey
 * @param filters - Optional filters for responses
 * @returns Promise<{success: boolean, message: string, payload: any}> - Operation result with responses
 *
 * @example
 * const result = await readResponsesAndAnalytics(surveyId, accountId, { page: 1, limit: 10 });
 * if (result.success) {
 *   console.log(`Found ${result.payload.responses.length} responses`);
 * }
 */
export const readResponsesAndAnalytics = async (surveyId: string, accountId: string, filters: ResponseFilters = {}) => {
  try {
    const { timeFilter, statusFilter, searchQuery, page = 1, limit = 10 } = filters;

    // Build where conditions
    const whereConditions: any = {
      survey_id: surveyId,
      account_id: accountId, // Security: Ensure account owns the survey
    };

    // Apply status filter
    if (statusFilter === 'completed') {
      whereConditions.is_discarded = false;
    } else if (statusFilter === 'discarded') {
      whereConditions.is_discarded = true;
    }
    // If statusFilter is 'all' or undefined, don't add is_discarded filter

    // Apply time filter
    if (timeFilter && timeFilter !== 'all-time') {
      const now = new Date();
      let startDate: Date;

      switch (timeFilter) {
        case '7-days':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30-days':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case '90-days':
          startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(0); // Beginning of time
      }

      whereConditions.created_at = {
        [Op.gte]: startDate,
      };
    }

    // Apply search query (search in response_data and respondent_details)
    if (searchQuery && searchQuery.trim()) {
      whereConditions[Op.or] = [
        {
          response_data: {
            [Op.iLike]: `%${searchQuery.trim()}%`,
          },
        },
        {
          respondent_details: {
            [Op.iLike]: `%${searchQuery.trim()}%`,
          },
        },
      ];
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Get total count for pagination
    const totalCount = await responseModel.count({
      where: whereConditions,
    });

    // Query responses with filters and pagination
    const responses = await responseModel.findAll({
      where: whereConditions,
      attributes: ['id', 'response_data', 'respondent_details', 'meta', 'created_at', 'is_discarded', 'discard_reason'],
      order: [['created_at', 'DESC']],
      limit,
      offset,
    });

    // Generate analytics data
    const analytics = await generateAnalytics(surveyId, accountId);

    return {
      success: true,
      message: `${Var.app.emoji.success} Responses retrieved`,
      payload: {
        responses,
        totalCount,
        analytics,
      },
    };
  } catch (error) {
    // Log error for debugging and monitoring
    logger.error('Error retrieving responses:', error);
    return {
      success: false,
      message: `${Var.app.emoji.failure} Could not retrieve responses`,
      payload: error,
    };
  }
};

// Helper function to generate analytics data
async function generateAnalytics(surveyId: string, accountId: string) {
  try {
    const allResponses = await responseModel.findAll({
      where: {
        survey_id: surveyId,
        account_id: accountId,
        is_discarded: false,
      },
      attributes: ['created_at', 'meta'],
      order: [['created_at', 'DESC']],
    });

    const totalResponses = allResponses.length;
    const avgCompletionTime = 0; // TODO: Calculate from meta data

    const responsesByDay: any = {};
    allResponses.forEach((response: any) => {
      const day = response.created_at.toISOString().split('T')[0];
      responsesByDay[day] = (responsesByDay[day] || 0) + 1;
    });

    const countryDistribution: any = {};
    // TODO: Extract country data from meta

    return {
      totalResponses,
      avgCompletionTime,
      responsesByDay,
      countryDistribution,
    };
  } catch (error) {
    logger.error('Error generating analytics:', error);
    return {
      totalResponses: 0,
      avgCompletionTime: 0,
      responsesByDay: {},
      countryDistribution: {},
    };
  }
}
